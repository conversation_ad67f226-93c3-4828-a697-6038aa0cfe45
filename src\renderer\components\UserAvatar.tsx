import React from 'react';
import { User } from '../types';

interface UserAvatarProps {
  user: User;
  size?: 'small' | 'medium' | 'large';
  showStatus?: boolean;
  className?: string;
  onClick?: () => void;
}

const UserAvatar: React.FC<UserAvatarProps> = ({
  user,
  size = 'medium',
  showStatus = true,
  className = '',
  onClick,
}) => {
  const sizeClasses = {
    small: 'w-6 h-6 text-xs',
    medium: 'w-8 h-8 text-sm',
    large: 'w-12 h-12 text-lg',
  };

  const statusSizeClasses = {
    small: 'w-2 h-2 -bottom-0.5 -right-0.5',
    medium: 'w-3 h-3 -bottom-0.5 -right-0.5',
    large: 'w-4 h-4 -bottom-1 -right-1',
  };

  const getStatusColor = () => {
    if (!user.isConnected) return 'bg-discord-gray-500';
    return 'bg-discord-green';
  };

  const getInitials = () => {
    return user.nickname
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div 
      className={`relative inline-block ${className} ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      {/* 头像 */}
      <div 
        className={`
          ${sizeClasses[size]} 
          bg-discord-blurple 
          rounded-full 
          flex 
          items-center 
          justify-center 
          font-semibold 
          text-white
          transition-transform
          duration-200
          ${onClick ? 'hover:scale-105' : ''}
        `}
      >
        {getInitials()}
      </div>

      {/* 在线状态指示器 */}
      {showStatus && (
        <div 
          className={`
            absolute 
            ${statusSizeClasses[size]} 
            ${getStatusColor()} 
            rounded-full 
            border-2 
            border-discord-gray-800
          `}
          title={
            !user.isConnected 
              ? '离线' 
              : '在线'
          }
        />
      )}
    </div>
  );
};

export default UserAvatar;
