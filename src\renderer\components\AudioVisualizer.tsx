import React, { useEffect, useRef } from 'react';

interface AudioVisualizerProps {
  isSpeaking: boolean;
  isConnected: boolean;
  isMuted: boolean;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

const AudioVisualizer: React.FC<AudioVisualizerProps> = ({
  isSpeaking,
  isConnected,
  isMuted,
  size = 'medium',
  className = '',
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  const sizeConfig = {
    small: { width: 24, height: 24, bars: 3 },
    medium: { width: 32, height: 32, bars: 4 },
    large: { width: 48, height: 48, bars: 5 },
  };

  const config = sizeConfig[size];

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 设置画布大小
    canvas.width = config.width;
    canvas.height = config.height;

    let animationTime = 0;

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      if (!isConnected) {
        // 未连接状态 - 显示灰色静态条
        drawStaticBars(ctx, '#6B7280');
      } else if (isMuted) {
        // 静音状态 - 显示红色静态条
        drawStaticBars(ctx, '#EF4444');
      } else if (isSpeaking) {
        // 说话状态 - 显示绿色动画条
        drawAnimatedBars(ctx, '#10B981', animationTime);
        animationTime += 0.1;
      } else {
        // 连接但未说话 - 显示蓝色静态条
        drawStaticBars(ctx, '#3B82F6');
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isSpeaking, isConnected, isMuted, config]);

  const drawStaticBars = (ctx: CanvasRenderingContext2D, color: string) => {
    const barWidth = 2;
    const barSpacing = 1;
    const totalBarsWidth = config.bars * barWidth + (config.bars - 1) * barSpacing;
    const startX = (config.width - totalBarsWidth) / 2;
    const baseHeight = config.height * 0.3;

    ctx.fillStyle = color;

    for (let i = 0; i < config.bars; i++) {
      const x = startX + i * (barWidth + barSpacing);
      const height = baseHeight;
      const y = (config.height - height) / 2;

      ctx.fillRect(x, y, barWidth, height);
    }
  };

  const drawAnimatedBars = (ctx: CanvasRenderingContext2D, color: string, time: number) => {
    const barWidth = 2;
    const barSpacing = 1;
    const totalBarsWidth = config.bars * barWidth + (config.bars - 1) * barSpacing;
    const startX = (config.width - totalBarsWidth) / 2;
    const maxHeight = config.height * 0.8;
    const minHeight = config.height * 0.2;

    ctx.fillStyle = color;

    for (let i = 0; i < config.bars; i++) {
      const x = startX + i * (barWidth + barSpacing);
      
      // 为每个条创建不同的动画相位
      const phase = time + i * 0.5;
      const amplitude = Math.sin(phase) * 0.5 + 0.5; // 0 到 1 之间
      const height = minHeight + amplitude * (maxHeight - minHeight);
      const y = (config.height - height) / 2;

      ctx.fillRect(x, y, barWidth, height);
    }
  };

  const getIndicatorColor = () => {
    if (!isConnected) return 'text-gray-400';
    if (isMuted) return 'text-red-400';
    if (isSpeaking) return 'text-green-400';
    return 'text-blue-400';
  };

  const getTooltipText = () => {
    if (!isConnected) return '未连接语音';
    if (isMuted) return '已静音';
    if (isSpeaking) return '说话中';
    return '已连接语音';
  };

  return (
    <div 
      className={`relative inline-flex items-center justify-center ${className}`}
      title={getTooltipText()}
    >
      <canvas
        ref={canvasRef}
        className={`${getIndicatorColor()}`}
        style={{ 
          width: config.width, 
          height: config.height,
          filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3))'
        }}
      />
      
      {/* 连接状态指示器 */}
      {isConnected && (
        <div className="absolute -top-1 -right-1">
          <div className={`w-2 h-2 rounded-full ${
            isSpeaking ? 'bg-green-400' : 'bg-blue-400'
          }`} />
        </div>
      )}
      
      {/* 静音指示器 */}
      {isMuted && (
        <div className="absolute -bottom-1 -right-1">
          <svg className="w-3 h-3 text-red-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.797-3.793a1 1 0 011.617.793zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </div>
      )}
    </div>
  );
};

export default AudioVisualizer;
