import { 
  ChannelVoiceState, 
  VoiceUser, 
  CallsSession, 
  CallsTrack,
  SessionDescription,
  CreateSessionRequest,
  PushTrackRequest,
  PullTrackRequest
} from '../types';
import { CloudflareCallsAPI } from '../utils/callsApi';
import { generateId } from '../utils';

export class VoiceManager {
  private callsApi: CloudflareCallsAPI;
  private channelVoiceStates: Map<string, ChannelVoiceState> = new Map();
  private userSessions: Map<string, string> = new Map(); // userId -> sessionId

  constructor(appId: string, secret: string) {
    this.callsApi = new CloudflareCallsAPI(appId, secret);
  }

  /**
   * 用户加入语音频道
   */
  async joinVoiceChannel(userId: string, channelId: string, correlationId?: string): Promise<{
    sessionId: string;
    sessionDescription: SessionDescription;
    iceServers: RTCIceServer[];
  }> {
    try {
      // 如果用户已经在其他频道，先离开
      await this.leaveVoiceChannel(userId);

      // 创建新的 Session
      const sessionResponse = await this.callsApi.createSession({
        correlationId: correlationId || `${userId}-${Date.now()}`
      });

      // 记录用户的 Session
      this.userSessions.set(userId, sessionResponse.sessionId);

      // 获取或创建频道语音状态
      let channelState = this.channelVoiceStates.get(channelId);
      if (!channelState) {
        channelState = {
          channelId,
          users: [],
          sessions: new Map(),
        };
        this.channelVoiceStates.set(channelId, channelState);
      }

      // 添加用户到频道
      const voiceUser: VoiceUser = {
        userId,
        sessionId: sessionResponse.sessionId,
        isMuted: false,
        isDeafened: false,
        isSpeaking: false,
        tracks: [],
      };

      // 移除用户如果已存在（防止重复）
      channelState.users = channelState.users.filter(u => u.userId !== userId);
      channelState.users.push(voiceUser);

      // 记录 Session
      const session: CallsSession = {
        sessionId: sessionResponse.sessionId,
        appId: this.callsApi['appId'],
        tracks: [],
        created: new Date().toISOString(),
        status: 'new',
      };
      channelState.sessions.set(sessionResponse.sessionId, session);

      console.log(`User ${userId} joined voice channel ${channelId} with session ${sessionResponse.sessionId}`);

      return sessionResponse;
    } catch (error) {
      console.error('Error joining voice channel:', error);
      throw error;
    }
  }

  /**
   * 用户离开语音频道
   */
  async leaveVoiceChannel(userId: string): Promise<void> {
    try {
      const sessionId = this.userSessions.get(userId);
      if (!sessionId) {
        return; // 用户不在任何语音频道中
      }

      // 找到用户所在的频道
      let userChannelId: string | null = null;
      for (const [channelId, channelState] of this.channelVoiceStates) {
        if (channelState.users.some(u => u.userId === userId)) {
          userChannelId = channelId;
          break;
        }
      }

      if (userChannelId) {
        const channelState = this.channelVoiceStates.get(userChannelId)!;
        
        // 从频道中移除用户
        channelState.users = channelState.users.filter(u => u.userId !== userId);
        
        // 移除 Session
        channelState.sessions.delete(sessionId);
        
        // 如果频道没有用户了，清理频道状态
        if (channelState.users.length === 0) {
          this.channelVoiceStates.delete(userChannelId);
        }
      }

      // 关闭 Cloudflare Session
      await this.callsApi.closeSession(sessionId);

      // 清理用户 Session 记录
      this.userSessions.delete(userId);

      console.log(`User ${userId} left voice channel with session ${sessionId}`);
    } catch (error) {
      console.error('Error leaving voice channel:', error);
      throw error;
    }
  }

  /**
   * 推送用户的音频轨道
   */
  async pushUserTrack(userId: string, trackName: string, sessionDescription: SessionDescription): Promise<{
    sessionDescription: SessionDescription;
    trackName: string;
    mid: string;
  }> {
    const sessionId = this.userSessions.get(userId);
    if (!sessionId) {
      throw new Error('User is not in a voice channel');
    }

    try {
      const response = await this.callsApi.pushTrack({
        sessionId,
        trackName,
        sessionDescription,
      });

      // 更新用户的轨道信息
      this.updateUserTrack(userId, {
        trackName: response.trackName,
        mid: response.mid,
        location: 'local',
        sessionId,
        status: 'active',
        kind: 'audio',
      });

      return response;
    } catch (error) {
      console.error('Error pushing user track:', error);
      throw error;
    }
  }

  /**
   * 拉取其他用户的音频轨道
   */
  async pullUserTrack(userId: string, targetTrackName: string, sessionDescription: SessionDescription): Promise<{
    sessionDescription: SessionDescription;
    trackName: string;
    mid: string;
  }> {
    const sessionId = this.userSessions.get(userId);
    if (!sessionId) {
      throw new Error('User is not in a voice channel');
    }

    try {
      const response = await this.callsApi.pullTrack({
        sessionId,
        trackName: targetTrackName,
        sessionDescription,
      });

      // 更新用户的轨道信息
      this.updateUserTrack(userId, {
        trackName: response.trackName,
        mid: response.mid,
        location: 'remote',
        sessionId,
        status: 'active',
        kind: 'audio',
      });

      return response;
    } catch (error) {
      console.error('Error pulling user track:', error);
      throw error;
    }
  }

  /**
   * 更新用户轨道信息
   */
  private updateUserTrack(userId: string, track: CallsTrack): void {
    for (const channelState of this.channelVoiceStates.values()) {
      const user = channelState.users.find(u => u.userId === userId);
      if (user) {
        // 移除同名轨道（如果存在）
        user.tracks = user.tracks.filter(t => t.trackName !== track.trackName);
        // 添加新轨道
        user.tracks.push(track);
        break;
      }
    }
  }

  /**
   * 更新用户语音状态
   */
  updateUserVoiceState(userId: string, updates: Partial<Pick<VoiceUser, 'isMuted' | 'isDeafened' | 'isSpeaking'>>): void {
    for (const channelState of this.channelVoiceStates.values()) {
      const user = channelState.users.find(u => u.userId === userId);
      if (user) {
        Object.assign(user, updates);
        break;
      }
    }
  }

  /**
   * 获取频道语音状态
   */
  getChannelVoiceState(channelId: string): ChannelVoiceState | undefined {
    return this.channelVoiceStates.get(channelId);
  }

  /**
   * 获取用户的 Session ID
   */
  getUserSessionId(userId: string): string | undefined {
    return this.userSessions.get(userId);
  }

  /**
   * 获取频道中的所有可用轨道
   */
  getChannelTracks(channelId: string): CallsTrack[] {
    const channelState = this.channelVoiceStates.get(channelId);
    if (!channelState) return [];

    const tracks: CallsTrack[] = [];
    for (const user of channelState.users) {
      tracks.push(...user.tracks.filter(t => t.location === 'local' && t.status === 'active'));
    }
    return tracks;
  }

  /**
   * 清理断开连接的用户
   */
  async cleanupDisconnectedUser(userId: string): Promise<void> {
    await this.leaveVoiceChannel(userId);
  }
}
