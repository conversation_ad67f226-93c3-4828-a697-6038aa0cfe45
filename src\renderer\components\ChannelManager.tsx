import React, { useState } from 'react';
import { Channel, User, VoiceState } from '../types';
import UserAvatar from './UserAvatar';
import AudioVisualizer from './AudioVisualizer';

interface ChannelManagerProps {
  channels: Channel[];
  currentChannel: Channel | null;
  user: User | null;
  onChannelSelect: (channel: Channel) => void;
  onChannelCreate: (name: string, type?: 'voice' | 'text') => void;
  onChannelDelete: (channelId: string) => void;
  onChannelRename: (channelId: string, newName: string) => void;
  voiceState?: VoiceState;
  className?: string;
}

const ChannelManager: React.FC<ChannelManagerProps> = ({
  channels,
  currentChannel,
  user,
  onChannelSelect,
  onChannelCreate,
  onChannelDelete,
  onChannelRename,
  voiceState,
  className = '',
}) => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newChannelName, setNewChannelName] = useState('');
  const [newChannelType, setNewChannelType] = useState<'voice' | 'text'>('text');
  const [editingChannel, setEditingChannel] = useState<string | null>(null);
  const [editName, setEditName] = useState('');
  const [contextMenu, setContextMenu] = useState<{
    channelId: string;
    x: number;
    y: number;
  } | null>(null);

  const handleCreateChannel = () => {
    if (newChannelName.trim()) {
      onChannelCreate(newChannelName.trim(), newChannelType);
      setNewChannelName('');
      setShowCreateModal(false);
    }
  };

  const handleStartEdit = (channel: Channel) => {
    setEditingChannel(channel.id);
    setEditName(channel.name);
    setContextMenu(null);
  };

  const handleSaveEdit = () => {
    if (editingChannel && editName.trim()) {
      onChannelRename(editingChannel, editName.trim());
      setEditingChannel(null);
      setEditName('');
    }
  };

  const handleCancelEdit = () => {
    setEditingChannel(null);
    setEditName('');
  };

  const handleContextMenu = (e: React.MouseEvent, channelId: string) => {
    e.preventDefault();
    setContextMenu({
      channelId,
      x: e.clientX,
      y: e.clientY,
    });
  };

  const handleDeleteChannel = (channelId: string) => {
    if (window.confirm('确定要删除这个频道吗？')) {
      onChannelDelete(channelId);
    }
    setContextMenu(null);
  };

  const getChannelIcon = (type: 'voice' | 'text') => {
    if (type === 'voice') {
      return (
        <svg className="w-4 h-4 text-discord-gray-400" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM15.657 6.343a1 1 0 011.414 0A9.972 9.972 0 0119 12a9.972 9.972 0 01-1.929 5.657 1 1 0 11-1.414-1.414A7.971 7.971 0 0017 12c0-1.594-.471-3.078-1.343-4.243a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      );
    } else {
      return (
        <svg className="w-4 h-4 text-discord-gray-400" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clipRule="evenodd" />
        </svg>
      );
    }
  };

  // 点击外部关闭上下文菜单
  React.useEffect(() => {
    const handleClickOutside = () => setContextMenu(null);
    if (contextMenu) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [contextMenu]);

  return (
    <div className={`${className}`}>
      {/* 频道列表标题 */}
      <div className="flex items-center justify-between mb-2 px-2">
        <span className="text-xs font-semibold text-discord-gray-400 uppercase tracking-wide">
          频道列表
        </span>
        <button
          onClick={() => setShowCreateModal(true)}
          className="w-4 h-4 flex items-center justify-center text-discord-gray-400 hover:text-gray-200 transition-colors duration-150"
          title="创建频道"
        >
          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
          </svg>
        </button>
      </div>

      {/* 频道列表 */}
      <div className="space-y-1">
        {channels.map((channel) => (
          <div key={channel.id} className="channel-container">
            <div
              className={`channel-item group ${
                currentChannel?.id === channel.id ? 'active' : ''
              }`}
              onClick={() => onChannelSelect(channel)}
              onContextMenu={(e) => handleContextMenu(e, channel.id)}
            >
              <div className="flex items-center flex-1">
                {getChannelIcon(channel.type)}
                {editingChannel === channel.id ? (
                  <input
                    type="text"
                    value={editName}
                    onChange={(e) => setEditName(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') handleSaveEdit();
                      if (e.key === 'Escape') handleCancelEdit();
                    }}
                    onBlur={handleSaveEdit}
                    className="bg-discord-gray-700 text-white text-sm px-1 py-0.5 rounded flex-1 ml-2 focus:outline-none"
                    autoFocus
                  />
                ) : (
                  <span className="text-sm text-discord-gray-300 flex-1 ml-2 truncate">
                    {channel.name}
                  </span>
                )}
              </div>
              
              {/* 快捷操作按钮 */}
              {editingChannel !== channel.id && (
                <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-150">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleStartEdit(channel);
                    }}
                    className="w-4 h-4 flex items-center justify-center text-discord-gray-400 hover:text-gray-200"
                    title="重命名"
                  >
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                    </svg>
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteChannel(channel.id);
                    }}
                    className="w-4 h-4 flex items-center justify-center text-discord-gray-400 hover:text-red-400"
                    title="删除"
                  >
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              )}
            </div>
            
            {/* 频道用户列表 - 始终显示，不再有折叠状态 */}
            {channel.users && channel.users.length > 0 && (
              <div className="channel-users pl-6 py-1">
                {channel.users.map(channelUser => (
                  <div key={channelUser.id} className="channel-user-item py-1 flex items-center">
                    <UserAvatar
                      user={channelUser}
                      size="small"
                      showStatus={true}
                      className="mr-2"
                    />
                    <span className="text-xs text-discord-gray-300 truncate flex-1">
                      {channelUser.nickname}
                    </span>

                    {/* 语音状态指示器 - 只在语音频道中显示 */}
                    {channel.type === 'voice' && voiceState && channelUser.id === user?.id && (
                      <AudioVisualizer
                        isSpeaking={voiceState.isSpeaking}
                        isConnected={voiceState.isConnected}
                        isMuted={voiceState.isMuted}
                        size="small"
                        className="ml-1"
                      />
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* 上下文菜单 */}
      {contextMenu && (
        <div
          className="fixed bg-discord-gray-800 border border-discord-gray-600 rounded-lg shadow-lg py-2 z-50"
          style={{
            left: contextMenu.x,
            top: contextMenu.y,
          }}
        >
          <button
            onClick={() => {
              const channel = channels.find(c => c.id === contextMenu.channelId);
              if (channel) handleStartEdit(channel);
            }}
            className="w-full px-4 py-2 text-left text-sm text-white hover:bg-discord-gray-700 transition-colors duration-150"
          >
            重命名频道
          </button>
          <button
            onClick={() => handleDeleteChannel(contextMenu.channelId)}
            className="w-full px-4 py-2 text-left text-sm text-red-400 hover:bg-discord-gray-700 transition-colors duration-150"
          >
            删除频道
          </button>
        </div>
      )}

      {/* 创建频道模态框 */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-discord-gray-700 rounded-lg p-6 w-96">
            <h3 className="text-lg font-semibold text-white mb-4">创建频道</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-discord-gray-300 mb-2">
                  频道类型
                </label>
                <div className="flex space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="voice"
                      checked={newChannelType === 'voice'}
                      onChange={(e) => setNewChannelType(e.target.value as 'voice')}
                      className="mr-2"
                    />
                    <span className="text-sm text-white">语音频道</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="text"
                      checked={newChannelType === 'text'}
                      onChange={(e) => setNewChannelType(e.target.value as 'text')}
                      className="mr-2"
                    />
                    <span className="text-sm text-white">文本频道</span>
                  </label>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-discord-gray-300 mb-2">
                  频道名称
                </label>
                <input
                  type="text"
                  value={newChannelName}
                  onChange={(e) => setNewChannelName(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') handleCreateChannel();
                    if (e.key === 'Escape') setShowCreateModal(false);
                  }}
                  placeholder="输入频道名称"
                  className="input-primary w-full"
                  autoFocus
                />
              </div>
            </div>
            
            <div className="flex justify-end space-x-2 mt-6">
              <button
                onClick={() => setShowCreateModal(false)}
                className="btn-secondary"
              >
                取消
              </button>
              <button
                onClick={handleCreateChannel}
                className="btn-primary"
                disabled={!newChannelName.trim()}
              >
                创建
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChannelManager;
