import { Env } from './types';
import { LobbyState } from './handlers/LobbyState';
import {
  handleCorsOptions,
  createJsonResponse,
  createErrorResponse,
  withErrorHandling,
  RateLimiter
} from './utils';

// 导出Durable Object类
export { LobbyState };

// 全局限流器
const rateLimiter = new RateLimiter(100, 60000); // 每分钟100个请求

export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    // 处理CORS预检请求
    if (request.method === 'OPTIONS') {
      return handleCorsOptions();
    }

    // 获取客户端IP进行限流
    const clientIP = request.headers.get('CF-Connecting-IP') || 'unknown';
    if (!rateLimiter.isAllowed(clientIP)) {
      return createJsonResponse(
        createErrorResponse('Rate limit exceeded'),
        429
      );
    }

    const url = new URL(request.url);
    const pathname = url.pathname;

    try {
      // 路由处理 - 所有请求都路由到全局 LobbyState
      if (pathname.startsWith('/api/') || pathname.startsWith('/ws') || request.headers.get('Upgrade') === 'websocket') {
        return await handleLobbyRequests(request, env);
      } else if (pathname === '/health') {
        return createJsonResponse({
          success: true,
          data: {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            version: '2.0.0'
          }
        });
      } else {
        return createJsonResponse(
          createErrorResponse('Not Found', 'The requested endpoint was not found'),
          404
        );
      }
    } catch (error) {
      console.error('Request handling error:', error);
      return createJsonResponse(
        createErrorResponse('Internal Server Error', error instanceof Error ? error.message : 'Unknown error'),
        500
      );
    }
  },
};

// 处理所有请求 - 路由到全局 LobbyState
async function handleLobbyRequests(request: Request, env: Env): Promise<Response> {
  // 获取全局大厅状态的Durable Object
  const lobbyId = 'global-lobby'; // 全局唯一的大厅
  const durableObjectId = env.LOBBY_STATE.idFromName(lobbyId);
  const lobbyState = env.LOBBY_STATE.get(durableObjectId);

  // 直接转发请求到 LobbyState，它会处理所有路由
  return await lobbyState.fetch(request);
}

// 定期清理任务（可选）
export async function scheduled(event: ScheduledEvent, env: Env, ctx: ExecutionContext): Promise<void> {
  // 清理限流器
  rateLimiter.cleanup();
  
  // 这里可以添加其他定期清理任务
  console.log('Scheduled cleanup completed');
}
