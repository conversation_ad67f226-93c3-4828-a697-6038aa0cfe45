import { useState, useEffect, useRef, useCallback } from 'react';
import { 
  VoiceState, 
  CallsSession, 
  CallsTrack, 
  SessionDescription, 
  AudioDevice,
  VoiceEvent 
} from '../types';

interface UseWebRTCProps {
  onVoiceEvent?: (event: VoiceEvent) => void;
  onError?: (error: string) => void;
}

export function useWebRTC({ onVoiceEvent, onError }: UseWebRTCProps = {}) {
  const [voiceState, setVoiceState] = useState<VoiceState>({
    isConnected: false,
    isMuted: false,
    isDeafened: false,
    isSpeaking: false,
    volume: 1.0,
  });

  const [audioDevices, setAudioDevices] = useState<AudioDevice[]>([]);
  const [selectedInputDevice, setSelectedInputDevice] = useState<string>('');
  const [selectedOutputDevice, setSelectedOutputDevice] = useState<string>('');

  const peerConnectionRef = useRef<RTCPeerConnection | null>(null);
  const localStreamRef = useRef<MediaStream | null>(null);
  const remoteStreamsRef = useRef<Map<string, MediaStream>>(new Map());
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const microphoneGainRef = useRef<GainNode | null>(null);
  const speakingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 初始化音频设备列表
  const initializeAudioDevices = useCallback(async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioDeviceList: AudioDevice[] = devices
        .filter(device => device.kind === 'audioinput' || device.kind === 'audiooutput')
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `${device.kind} ${device.deviceId.slice(0, 8)}`,
          kind: device.kind as 'audioinput' | 'audiooutput',
        }));

      setAudioDevices(audioDeviceList);

      // 设置默认设备
      const defaultInput = audioDeviceList.find(d => d.kind === 'audioinput');
      const defaultOutput = audioDeviceList.find(d => d.kind === 'audiooutput');
      
      if (defaultInput && !selectedInputDevice) {
        setSelectedInputDevice(defaultInput.deviceId);
      }
      if (defaultOutput && !selectedOutputDevice) {
        setSelectedOutputDevice(defaultOutput.deviceId);
      }
    } catch (error) {
      console.error('Failed to enumerate audio devices:', error);
      onError?.('Failed to access audio devices');
    }
  }, [selectedInputDevice, selectedOutputDevice, onError]);

  // 初始化音频上下文和分析器
  const initializeAudioContext = useCallback(() => {
    if (!audioContextRef.current) {
      audioContextRef.current = new AudioContext();
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 256;
      microphoneGainRef.current = audioContextRef.current.createGain();
    }
  }, []);

  // 获取用户媒体流
  const getUserMedia = useCallback(async (deviceId?: string): Promise<MediaStream> => {
    try {
      const constraints: MediaStreamConstraints = {
        audio: {
          deviceId: deviceId ? { exact: deviceId } : undefined,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
        video: false,
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      localStreamRef.current = stream;

      // 设置音频分析
      initializeAudioContext();
      if (audioContextRef.current && analyserRef.current && microphoneGainRef.current) {
        const source = audioContextRef.current.createMediaStreamSource(stream);
        source.connect(microphoneGainRef.current);
        microphoneGainRef.current.connect(analyserRef.current);
        
        // 开始语音活动检测
        startVoiceActivityDetection();
      }

      return stream;
    } catch (error) {
      console.error('Failed to get user media:', error);
      throw new Error('Failed to access microphone');
    }
  }, [initializeAudioContext]);

  // 语音活动检测
  const startVoiceActivityDetection = useCallback(() => {
    if (!analyserRef.current) return;

    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
    
    const detectVoiceActivity = () => {
      if (!analyserRef.current || voiceState.isMuted) {
        requestAnimationFrame(detectVoiceActivity);
        return;
      }

      analyserRef.current.getByteFrequencyData(dataArray);
      
      // 计算音频能量
      const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
      const threshold = 30; // 可调整的阈值
      
      const isSpeaking = average > threshold;
      
      if (isSpeaking !== voiceState.isSpeaking) {
        setVoiceState(prev => ({ ...prev, isSpeaking }));
        
        // 清除之前的超时
        if (speakingTimeoutRef.current) {
          clearTimeout(speakingTimeoutRef.current);
        }
        
        // 如果停止说话，延迟一段时间再更新状态
        if (!isSpeaking) {
          speakingTimeoutRef.current = setTimeout(() => {
            setVoiceState(prev => ({ ...prev, isSpeaking: false }));
          }, 500);
        }
      }
      
      requestAnimationFrame(detectVoiceActivity);
    };
    
    detectVoiceActivity();
  }, [voiceState.isMuted, voiceState.isSpeaking]);

  // 创建 PeerConnection
  const createPeerConnection = useCallback((iceServers: RTCIceServer[] = []) => {
    const pc = new RTCPeerConnection({
      iceServers: iceServers.length > 0 ? iceServers : [
        { urls: 'stun:stun.l.google.com:19302' }
      ],
    });

    pc.onicecandidate = (event) => {
      if (event.candidate) {
        console.log('ICE candidate:', event.candidate);
      }
    };

    pc.ontrack = (event) => {
      console.log('Received remote track:', event.track);
      const [remoteStream] = event.streams;
      if (remoteStream) {
        remoteStreamsRef.current.set(remoteStream.id, remoteStream);
      }
    };

    pc.onconnectionstatechange = () => {
      console.log('Connection state:', pc.connectionState);
      setVoiceState(prev => ({
        ...prev,
        isConnected: pc.connectionState === 'connected'
      }));
    };

    peerConnectionRef.current = pc;
    return pc;
  }, []);

  // 静音/取消静音
  const toggleMute = useCallback(() => {
    if (localStreamRef.current) {
      const audioTrack = localStreamRef.current.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = voiceState.isMuted;
        setVoiceState(prev => ({ ...prev, isMuted: !prev.isMuted }));
      }
    }
  }, [voiceState.isMuted]);

  // 关闭/开启声音
  const toggleDeafen = useCallback(() => {
    setVoiceState(prev => ({ ...prev, isDeafened: !prev.isDeafened }));
    
    // 控制所有远程音频流的音量
    remoteStreamsRef.current.forEach(stream => {
      stream.getAudioTracks().forEach(track => {
        track.enabled = voiceState.isDeafened;
      });
    });
  }, [voiceState.isDeafened]);

  // 设置音量
  const setVolume = useCallback((volume: number) => {
    const clampedVolume = Math.max(0, Math.min(1, volume));
    setVoiceState(prev => ({ ...prev, volume: clampedVolume }));
    
    if (microphoneGainRef.current) {
      microphoneGainRef.current.gain.value = clampedVolume;
    }
  }, []);

  // 切换输入设备
  const switchInputDevice = useCallback(async (deviceId: string) => {
    try {
      setSelectedInputDevice(deviceId);
      
      if (localStreamRef.current) {
        // 停止当前流
        localStreamRef.current.getTracks().forEach(track => track.stop());
        
        // 获取新的流
        const newStream = await getUserMedia(deviceId);
        
        // 更新 PeerConnection 中的轨道
        if (peerConnectionRef.current) {
          const sender = peerConnectionRef.current.getSenders().find(s => 
            s.track && s.track.kind === 'audio'
          );
          if (sender && newStream.getAudioTracks()[0]) {
            await sender.replaceTrack(newStream.getAudioTracks()[0]);
          }
        }
      }
    } catch (error) {
      console.error('Failed to switch input device:', error);
      onError?.('Failed to switch microphone');
    }
  }, [getUserMedia, onError]);

  // 清理资源
  const cleanup = useCallback(() => {
    if (localStreamRef.current) {
      localStreamRef.current.getTracks().forEach(track => track.stop());
      localStreamRef.current = null;
    }
    
    if (peerConnectionRef.current) {
      peerConnectionRef.current.close();
      peerConnectionRef.current = null;
    }
    
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
    
    if (speakingTimeoutRef.current) {
      clearTimeout(speakingTimeoutRef.current);
    }
    
    remoteStreamsRef.current.clear();
    
    setVoiceState({
      isConnected: false,
      isMuted: false,
      isDeafened: false,
      isSpeaking: false,
      volume: 1.0,
    });
  }, []);

  // 初始化
  useEffect(() => {
    initializeAudioDevices();
    
    // 监听设备变化
    navigator.mediaDevices.addEventListener('devicechange', initializeAudioDevices);
    
    return () => {
      navigator.mediaDevices.removeEventListener('devicechange', initializeAudioDevices);
      cleanup();
    };
  }, [initializeAudioDevices, cleanup]);

  // 创建 SDP Offer
  const createOffer = useCallback(async (): Promise<SessionDescription> => {
    if (!peerConnectionRef.current) {
      throw new Error('PeerConnection not initialized');
    }

    const offer = await peerConnectionRef.current.createOffer();
    await peerConnectionRef.current.setLocalDescription(offer);

    return {
      sdp: offer.sdp!,
      type: offer.type as 'offer'
    };
  }, []);

  // 创建 SDP Answer
  const createAnswer = useCallback(async (offer: SessionDescription): Promise<SessionDescription> => {
    if (!peerConnectionRef.current) {
      throw new Error('PeerConnection not initialized');
    }

    await peerConnectionRef.current.setRemoteDescription(new RTCSessionDescription(offer));
    const answer = await peerConnectionRef.current.createAnswer();
    await peerConnectionRef.current.setLocalDescription(answer);

    return {
      sdp: answer.sdp!,
      type: answer.type as 'answer'
    };
  }, []);

  // 设置远程 SDP
  const setRemoteDescription = useCallback(async (description: SessionDescription) => {
    if (!peerConnectionRef.current) {
      throw new Error('PeerConnection not initialized');
    }

    await peerConnectionRef.current.setRemoteDescription(new RTCSessionDescription(description));
  }, []);

  // 添加本地音频轨道
  const addLocalAudioTrack = useCallback(async () => {
    if (!peerConnectionRef.current || !localStreamRef.current) {
      throw new Error('PeerConnection or local stream not available');
    }

    const audioTrack = localStreamRef.current.getAudioTracks()[0];
    if (audioTrack) {
      peerConnectionRef.current.addTrack(audioTrack, localStreamRef.current);
      return audioTrack;
    }

    throw new Error('No audio track available');
  }, []);

  return {
    voiceState,
    audioDevices,
    selectedInputDevice,
    selectedOutputDevice,
    localStream: localStreamRef.current,
    remoteStreams: remoteStreamsRef.current,

    // 基础方法
    getUserMedia,
    createPeerConnection,
    toggleMute,
    toggleDeafen,
    setVolume,
    switchInputDevice,
    cleanup,

    // WebRTC 信令方法
    createOffer,
    createAnswer,
    setRemoteDescription,
    addLocalAudioTrack,
  };
}
