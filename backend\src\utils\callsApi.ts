import { 
  CreateSessionRequest, 
  CreateSessionResponse, 
  PushTrackRequest, 
  PushTrackResponse,
  PullTrackRequest,
  PullTrackResponse,
  SessionDescription 
} from '../types';

export class CloudflareCallsAPI {
  private appId: string;
  private secret: string;
  private baseUrl: string = 'https://rtc.live.cloudflare.com/v1';

  constructor(appId: string, secret: string) {
    this.appId = appId;
    this.secret = secret;
  }

  private getHeaders(): HeadersInit {
    return {
      'Authorization': `Bearer ${this.secret}`,
      'Content-Type': 'application/json',
    };
  }

  /**
   * 创建新的 WebRTC Session
   */
  async createSession(request: CreateSessionRequest = {}): Promise<CreateSessionResponse> {
    const url = `${this.baseUrl}/apps/${this.appId}/sessions/new`;
    const queryParams = new URLSearchParams();
    
    if (request.thirdparty !== undefined) {
      queryParams.append('thirdparty', request.thirdparty.toString());
    }
    if (request.correlationId) {
      queryParams.append('correlationId', request.correlationId);
    }

    const fullUrl = queryParams.toString() ? `${url}?${queryParams}` : url;

    const response = await fetch(fullUrl, {
      method: 'POST',
      headers: this.getHeaders(),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to create session: ${response.status} ${error}`);
    }

    const data = await response.json();
    return {
      sessionId: data.sessionId,
      sessionDescription: data.sessionDescription,
      iceServers: data.iceServers || [],
    };
  }

  /**
   * 推送音频轨道到 Session
   */
  async pushTrack(request: PushTrackRequest): Promise<PushTrackResponse> {
    const url = `${this.baseUrl}/apps/${this.appId}/sessions/${request.sessionId}/tracks/new`;

    const response = await fetch(url, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({
        trackName: request.trackName,
        sessionDescription: request.sessionDescription,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to push track: ${response.status} ${error}`);
    }

    const data = await response.json();
    return {
      sessionDescription: data.sessionDescription,
      trackName: data.trackName,
      mid: data.mid,
    };
  }

  /**
   * 拉取音频轨道从其他 Session
   */
  async pullTrack(request: PullTrackRequest): Promise<PullTrackResponse> {
    const url = `${this.baseUrl}/apps/${this.appId}/sessions/${request.sessionId}/tracks/${request.trackName}/pull`;

    const response = await fetch(url, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({
        sessionDescription: request.sessionDescription,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to pull track: ${response.status} ${error}`);
    }

    const data = await response.json();
    return {
      sessionDescription: data.sessionDescription,
      trackName: data.trackName,
      mid: data.mid,
    };
  }

  /**
   * 关闭 Session
   */
  async closeSession(sessionId: string): Promise<void> {
    const url = `${this.baseUrl}/apps/${this.appId}/sessions/${sessionId}`;

    const response = await fetch(url, {
      method: 'DELETE',
      headers: this.getHeaders(),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to close session: ${response.status} ${error}`);
    }
  }

  /**
   * 获取 Session 信息
   */
  async getSession(sessionId: string): Promise<any> {
    const url = `${this.baseUrl}/apps/${this.appId}/sessions/${sessionId}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: this.getHeaders(),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to get session: ${response.status} ${error}`);
    }

    return await response.json();
  }

  /**
   * 获取所有可用的 Tracks
   */
  async getTracks(): Promise<any> {
    const url = `${this.baseUrl}/apps/${this.appId}/tracks`;

    const response = await fetch(url, {
      method: 'GET',
      headers: this.getHeaders(),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to get tracks: ${response.status} ${error}`);
    }

    return await response.json();
  }
}
