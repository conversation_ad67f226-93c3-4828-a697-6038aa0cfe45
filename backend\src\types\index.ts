// 环境变量类型
export interface Env {
  LOBBY_STATE: DurableObjectNamespace;
  LOBBY_KV: KVNamespace;
  CLOUDFLARE_CALLS_APP_ID: string;
  CLOUDFLARE_CALLS_SECRET: string;
  CORS_ORIGIN: string;
}

// 用户类型
export interface User {
  id: string;
  nickname: string;
  isConnected: boolean;
  joinedAt: string;
}

// 频道类型
export interface Channel {
  id: string;
  name: string;
  type: 'voice' | 'text';
  userCount: number;
  users: User[];
  createdAt: string;
  updatedAt: string;
}

// 房间类型
export interface Room {
  id: string;
  name: string;
  channels: Channel[];
  users: User[];
  createdAt: string;
  updatedAt: string;
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  userId?: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Cloudflare Calls相关类型
export interface CallsSession {
  sessionId: string;
  appId: string;
  tracks: CallsTrack[];
  created: string;
  status: 'new' | 'connected' | 'disconnected';
}

export interface CallsTrack {
  trackName: string;
  mid: string;
  location: 'local' | 'remote';
  sessionId?: string;
  status: 'active' | 'inactive' | 'waiting';
  kind: 'audio' | 'video';
}

export interface SessionDescription {
  sdp: string;
  type: 'offer' | 'answer';
}

// Cloudflare Calls API 请求/响应类型
export interface CreateSessionRequest {
  thirdparty?: boolean;
  correlationId?: string;
}

export interface CreateSessionResponse {
  sessionId: string;
  sessionDescription: SessionDescription;
  iceServers: RTCIceServer[];
}

export interface PushTrackRequest {
  sessionId: string;
  trackName: string;
  sessionDescription: SessionDescription;
}

export interface PushTrackResponse {
  sessionDescription: SessionDescription;
  trackName: string;
  mid: string;
}

export interface PullTrackRequest {
  sessionId: string;
  trackName: string;
  sessionDescription: SessionDescription;
}

export interface PullTrackResponse {
  sessionDescription: SessionDescription;
  trackName: string;
  mid: string;
}

// 频道语音状态
export interface ChannelVoiceState {
  channelId: string;
  users: VoiceUser[];
  sessions: Map<string, CallsSession>;
}

export interface VoiceUser {
  userId: string;
  sessionId?: string;
  isMuted: boolean;
  isDeafened: boolean;
  isSpeaking: boolean;
  tracks: CallsTrack[];
}

// 请求类型
export interface CreateChannelRequest {
  name: string;
  type?: 'voice' | 'text';
  createdBy?: string;
}

export interface UpdateChannelRequest {
  name?: string;
  updatedBy?: string;
}

export interface JoinChannelRequest {
  channelId: string;
  userId: string;
}

export interface LeaveChannelRequest {
  channelId: string;
  userId: string;
}

export interface UpdateUserRequest {
  nickname?: string;
  isMuted?: boolean;
  isDeafened?: boolean;
  volume?: number;
}

// WebRTC信令相关类型
export interface SignalingMessage {
  type: 'offer' | 'answer' | 'ice-candidate' | 'join' | 'leave';
  payload: any;
  senderId: string;
  recipientId?: string;
  channelId: string;
}

// 事件类型
export interface RoomEvent {
  type: string;
  payload: any;
  timestamp: string;
  userId?: string;
}

// Durable Object状态类型
export interface RoomStateData {
  room: Room;
  lastActivity: string;
}

export interface UserStateData {
  user: User;
  connectionId?: string;
  lastSeen: string;
}
