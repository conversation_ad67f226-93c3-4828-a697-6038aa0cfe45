# Cloudflare Calls API 语音通信集成测试指南

## 概述

我们已经成功集成了 Cloudflare Calls API 语音通信功能到现有的频道系统中。以下是测试和验证功能的指南。

## 已实现的功能

### 1. 后端集成 ✅
- **Cloudflare Calls API 工具类** (`backend/src/utils/callsApi.ts`)
  - 创建 Session
  - 推送/拉取 Tracks
  - 管理 WebRTC 连接

- **语音管理器** (`backend/src/handlers/VoiceManager.ts`)
  - 用户加入/离开语音频道
  - 轨道管理
  - 语音状态更新

- **LobbyState 集成** (`backend/src/handlers/LobbyState.ts`)
  - 新增语音相关的 WebSocket 消息处理
  - `JOIN_VOICE`, `LEAVE_VOICE`, `PUSH_TRACK`, `PULL_TRACK`, `UPDATE_VOICE_STATE`

### 2. 前端 WebRTC 管理 ✅
- **useWebRTC Hook** (`src/renderer/hooks/useWebRTC.ts`)
  - WebRTC 连接管理
  - 音频流处理
  - 语音活动检测
  - 音频设备管理

- **useCloudflareVoice Hook** (`src/renderer/hooks/useCloudflareVoice.ts`)
  - Cloudflare Calls API 集成
  - Session 和 Track 管理
  - 事件处理

### 3. 语音控制组件 ✅
- **VoiceControls** (`src/renderer/components/VoiceControls.tsx`)
  - 静音/取消静音
  - 关闭/开启声音
  - 音量控制
  - 音频设备选择
  - 加入/离开语音按钮

- **AudioVisualizer** (`src/renderer/components/AudioVisualizer.tsx`)
  - 语音活动可视化
  - 连接状态指示
  - 静音状态显示

- **VoiceActivityIndicator** (`src/renderer/components/VoiceActivityIndicator.tsx`)
  - 实时音频波形显示
  - 高级语音活动检测

### 4. 系统集成 ✅
- **App.tsx** 更新
  - 语音事件处理
  - 频道切换时的语音管理
  - 语音控制组件集成

- **Sidebar 和 ChannelManager** 更新
  - 语音状态传递
  - 用户语音状态显示

## 测试步骤

### 1. 环境准备
```bash
# 确保环境变量配置正确
cp .env.example .env
# 编辑 .env 文件，设置正确的 Cloudflare Calls API 凭据
```

### 2. 启动应用
```bash
# 启动后端
cd backend
npm run dev

# 启动前端
npm run dev
```

### 3. 功能测试

#### 基础连接测试
1. 打开应用
2. 创建或选择一个语音频道
3. 检查是否显示语音控制面板
4. 点击"加入语音"按钮
5. 验证是否请求麦克风权限
6. 检查连接状态指示器

#### 语音功能测试
1. **静音/取消静音**
   - 点击静音按钮
   - 验证按钮状态变化
   - 检查语音活动指示器变化

2. **关闭/开启声音**
   - 点击关闭声音按钮
   - 验证按钮状态变化

3. **音量控制**
   - 调整音量滑块
   - 验证音量变化

4. **设备切换**
   - 打开设备设置
   - 切换不同的麦克风
   - 验证设备切换成功

#### 语音活动检测测试
1. 对着麦克风说话
2. 验证语音活动指示器显示"说话中"状态
3. 检查实时波形显示（如果启用）
4. 验证语音活动检测的灵敏度

#### 多用户测试
1. 在不同浏览器/设备上打开应用
2. 让多个用户加入同一语音频道
3. 验证用户列表中的语音状态显示
4. 测试音频传输质量

## 预期行为

### 正常流程
1. 用户选择语音频道 → 显示语音控制面板
2. 点击"加入语音" → 请求麦克风权限 → 创建 Cloudflare Session
3. 推送本地音频轨道 → 拉取其他用户轨道
4. 实时语音通信建立
5. 语音活动检测工作正常
6. 离开频道时清理所有资源

### 错误处理
- 麦克风权限被拒绝 → 显示错误提示
- 网络连接问题 → 显示连接失败
- Cloudflare API 错误 → 显示相应错误信息

## 调试信息

### 浏览器控制台
- 查看 WebSocket 连接状态
- 监控语音事件日志
- 检查 WebRTC 连接状态

### 网络面板
- 验证 Cloudflare Calls API 请求
- 检查 WebSocket 消息流

### 后端日志
- 监控 Durable Object 日志
- 查看语音管理器操作日志

## 已知限制

1. **浏览器兼容性**
   - 需要支持 WebRTC 的现代浏览器
   - 需要 HTTPS 环境（生产环境）

2. **音频质量**
   - 依赖网络质量
   - 受设备音频硬件影响

3. **并发限制**
   - Cloudflare Calls API 有并发连接限制
   - 需要根据实际使用情况调整

## 下一步优化

1. **音频质量优化**
   - 实现自适应比特率
   - 添加噪音抑制
   - 回声消除优化

2. **用户体验改进**
   - 添加音频测试功能
   - 实现推送通知
   - 优化连接重试逻辑

3. **性能优化**
   - 实现连接池
   - 优化内存使用
   - 减少 CPU 占用

## 故障排除

### 常见问题
1. **无法加入语音频道**
   - 检查麦克风权限
   - 验证 Cloudflare API 凭据
   - 检查网络连接

2. **听不到其他用户声音**
   - 检查扬声器设置
   - 验证音频输出设备
   - 检查音量设置

3. **语音活动检测不工作**
   - 检查麦克风输入
   - 调整检测阈值
   - 验证音频上下文初始化

通过以上测试步骤，可以全面验证 Cloudflare Calls API 语音通信功能的集成效果。
