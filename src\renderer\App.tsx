import React, { useState, useEffect, useCallback } from 'react';
import { User, Channel, ChannelState, AppEvent } from './types';
import { generateRandomNickname, generateId, userStorage } from './utils';
import { useWebSocket } from './hooks/useWebSocket';
import TitleBar from './components/TitleBar';
import Sidebar from './components/Sidebar';
import MainContent from './components/MainContent';
import ConnectionStatus from './components/ConnectionStatus';

const App: React.FC = () => {
  const [user, setUser] = useState<User | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [channels, setChannels] = useState<Channel[]>([]);
  const [currentChannel, setCurrentChannel] = useState<Channel | null>(null);
  const [channelState, setChannelState] = useState<ChannelState>({
    isConnected: false,
    currentChannelId: undefined,
  });
  const [isLoading, setIsLoading] = useState(true); // 添加加载状态

  // 处理应用事件
  const handleAppEvent = useCallback((event: AppEvent) => {
    console.log('App event received:', event);
    switch (event.type) {
      case 'ROOM_STATE_UPDATE':
        console.log('收到房间状态更新:', event.payload);
        if (event.payload.channels) {
          setChannels(event.payload.channels);
        }
        if (event.payload.users) {
          setUsers(event.payload.users);
        }
        setIsLoading(false); // 收到服务器数据后设置加载状态为 false
        break;
      case 'USER_JOIN':
        console.log('处理USER_JOIN事件:', event.payload);
        setUsers(prev => {
          const existingIndex = prev.findIndex(u => u.id === event.payload.id);
          if (existingIndex >= 0) {
            // 更新现有用户
            const updated = [...prev];
            updated[existingIndex] = { ...event.payload, isConnected: true };
            return updated;
          } else {
            // 添加新用户
            return [...prev, { ...event.payload, isConnected: true }];
          }
        });
        break;
      case 'USER_LEAVE':
        console.log('处理USER_LEAVE事件:', event.payload);
        setUsers(prev => prev.filter(u => u.id !== event.payload.userId));
        break;
      case 'USER_UPDATE':
        setUsers(prev => prev.map(u => 
          u.id === event.payload.id ? { ...u, ...event.payload } : u
        ));
        break;
      case 'USER_LIST_UPDATE':
        setUsers(event.payload);
        break;
      case 'CHANNEL_CREATE':
        setChannels(prev => [...prev, event.payload]);
        break;
      case 'CHANNEL_DELETE':
        setChannels(prev => prev.filter(c => c.id !== event.payload.channelId));
        if (currentChannel?.id === event.payload.channelId) {
          setCurrentChannel(null);
        }
        break;
      case 'CHANNEL_UPDATE':
        setChannels(prev => prev.map(c =>
          c.id === event.payload.id ? event.payload : c
        ));
        
        // 如果更新的是当前选中的频道，也更新currentChannel状态
        if (currentChannel?.id === event.payload.id) {
          setCurrentChannel(event.payload);
        }
        break;
      case 'USER_JOIN_CHANNEL':
        console.log('处理USER_JOIN_CHANNEL事件:', event.payload);
        setChannels(prev => {
          return prev.map(c => {
            if (c.id === event.payload.channelId) {
              // 确保不重复添加用户
              if (event.payload.user && !c.users.some(u => u.id === event.payload.user.id)) {
                console.log(`添加用户 ${event.payload.user.nickname} 到频道 ${c.name}`);
                const updatedChannel = {
                  ...c,
                  users: [...c.users, event.payload.user],
                  userCount: c.userCount + 1,
                };
                
                // 如果这是当前选中的频道，也更新currentChannel状态
                if (currentChannel && currentChannel.id === c.id) {
                  setCurrentChannel(updatedChannel);
                }
                
                return updatedChannel;
              }
            }
            return c;
          });
        });
        break;
      case 'USER_LEAVE_CHANNEL':
        console.log('处理USER_LEAVE_CHANNEL事件:', event.payload);
        setChannels(prev => prev.map(c => {
          if (c.id === event.payload.channelId) {
            const newUsers = c.users.filter(u => u.id !== event.payload.userId);
            console.log(`从频道 ${c.name} 移除用户，剩余用户数: ${newUsers.length}`);
            const updatedChannel = {
              ...c,
              users: newUsers,
              userCount: newUsers.length,
            };
            
            // 如果这是当前选中的频道，也更新currentChannel状态
            if (currentChannel && currentChannel.id === c.id) {
              setCurrentChannel(updatedChannel);
            }
            
            return updatedChannel;
          }
          return c;
        }));
        break;
      default:
        console.log('Unhandled app event:', event);
    }
  }, [currentChannel]);

  // 初始化用户
  useEffect(() => {
    const initUser = () => {
      let existingUser = userStorage.getUser();

      if (existingUser) {
        // 如果找到了旧用户，更新其在线状态并使用
        existingUser.isConnected = false;
        setUser(existingUser);
      } else {
        // 如果是新用户，则创建一个
        const newUser: User = {
          id: generateId(),
          nickname: generateRandomNickname(),
          isConnected: false,
          joinedAt: new Date(),
        };
        userStorage.saveUser(newUser);
        setUser(newUser);
      }
      
      // 增加会话计数
      userStorage.incrementSessionCount();
    };

    initUser();
  }, []);

  // 初始化WebSocket hook
  const webSocket = useWebSocket({
    user,
    onEvent: handleAppEvent,
  });

  const handleChannelSelect = async (channel: Channel) => {
    // 如果选择的频道与当前频道相同且已连接，则不执行任何操作
    if (channelState.isConnected && channelState.currentChannelId === channel.id) {
      console.log(`已经在频道 ${channel.name} 中，不需要重新加入`);
      return;
    }

    if (!webSocket.isConnected) {
      console.warn('WebSocket未连接，正在尝试连接...');
      
      // 显示正在连接的消息，可以通过状态或Toast实现
      // ...
      
      // 延迟执行，给WebSocket一些连接时间
      setTimeout(() => {
        if (webSocket.isConnected) {
          handleChannelSelect(channel); // 递归调用，尝试再次选择频道
        } else {
          console.error('WebSocket连接失败，无法加入频道');
          // 显示连接失败的消息，可以通过状态或Toast实现
          // ...
        }
      }, 2000);
      return;
    }

    // 如果已经在某个频道中，先离开
    if (channelState.isConnected && channelState.currentChannelId) {
      console.log(`离开当前频道: ${channelState.currentChannelId}`);
      webSocket.leaveChannel(channelState.currentChannelId);
    }

    console.log(`选择频道: ${channel.name} (${channel.id})`);
    
    // 只设置当前频道，不在本地添加用户
    // 用户将通过WebSocket事件添加到频道用户列表中
    setCurrentChannel(channel);

    try {
      console.log(`尝试加入频道: ${channel.name}`);
      webSocket.joinChannel(channel.id);
      setChannelState({
        isConnected: true,
        currentChannelId: channel.id
      });
      console.log(`成功加入频道: ${channel.name}`);
    } catch (error) {
      console.error('加入频道失败:', error);
      // 显示错误消息，可以通过状态或Toast实现
      // ...
    }
  };

  // 退出当前频道
  const handleLeaveChannel = () => {
    if (channelState.isConnected && channelState.currentChannelId) {
      console.log(`主动离开频道: ${channelState.currentChannelId}`);
      webSocket.leaveChannel(channelState.currentChannelId);
      
      // 更新频道状态
      setChannelState({
        isConnected: false,
        currentChannelId: undefined
      });
      
      // 保留currentChannel引用，但不再标记为已连接
      console.log('已退出频道');
    }
  };

  const handleChannelCreate = (name: string, type: 'voice' | 'text' = 'text') => {
    webSocket.createChannel(name, type);
  };

  const handleChannelDelete = (channelId: string) => {
    webSocket.deleteChannel(channelId);
  };

  const handleChannelRename = (channelId: string, newName: string) => {
    webSocket.renameChannel(channelId, newName);
  };

  const handleUserUpdate = (updates: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...updates };
      setUser(updatedUser);
      userStorage.saveUser(updatedUser);
      webSocket.updateUser(updatedUser);
    }
  };

  const handleChannelStateChange = (newState: Partial<ChannelState>) => {
    setChannelState(prev => ({ ...prev, ...newState }));
  };

  return (
    <div className="flex flex-col h-screen bg-discord-gray-900 text-white">
      {/* 标题栏 */}
      <TitleBar />

      {/* 主要内容 */}
      <div className="flex flex-1 overflow-hidden">
        {/* 侧边栏 */}
        <Sidebar
          user={user}
          users={users}
          channels={channels}
          currentChannel={currentChannel}
          onChannelSelect={handleChannelSelect}
          onChannelCreate={handleChannelCreate}
          onChannelDelete={handleChannelDelete}
          onChannelRename={handleChannelRename}
          onUserUpdate={handleUserUpdate}
          isLoading={isLoading} // 传递加载状态给侧边栏
        />

        {/* 主内容区域 */}
        <MainContent
          currentChannel={currentChannel}
          user={user || { id: '', nickname: '未登录', isConnected: false, joinedAt: new Date() }}
          channelState={channelState}
          onLeaveChannel={handleLeaveChannel}
        />
      </div>

      {/* 连接状态 */}
      <ConnectionStatus isConnected={webSocket.isConnected} />
    </div>
  );
};

export default App;
