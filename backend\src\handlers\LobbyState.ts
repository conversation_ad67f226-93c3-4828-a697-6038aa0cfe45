import { DurableObject } from 'cloudflare:workers';
import { generateId, formatTimestamp, broadcastToConnections } from '../utils';
import { User, Channel, WebSocketMessage, RoomEvent, Env } from '../types';

interface LobbyData {
  id: string;
  name: string;
  users: User[];
  channels: Channel[];
  createdAt: string;
  updatedAt: string;
}

export class LobbyState extends DurableObject {
  private lobby: LobbyData;
  private connections: Map<string, WebSocket>;
  private lastActivity: number;
  private state: DurableObjectState;

  constructor(ctx: DurableObjectState, env: Env) {
    super(ctx, env);
    
    // 保存 state 引用以便稍后使用
    this.state = ctx;
    
    // 初始化默认数据，稍后将尝试从存储加载
    this.lobby = {
      id: 'global-lobby',
      name: 'OPZ Voice Chat',
      users: [],
      channels: [
        {
          id: 'general',
          name: '大厅',
          type: 'voice',
          users: [],
          userCount: 0,
          createdAt: formatTimestamp(),
          updatedAt: formatTimestamp(),
        }
      ],
      createdAt: formatTimestamp(),
      updatedAt: formatTimestamp(),
    };
    
    this.connections = new Map();
    this.lastActivity = Date.now();
    
    // 从存储加载数据
    this.loadFromStorage().catch(err => {
      console.error('从存储加载数据失败:', err);
    });
    
    console.log('LobbyState initialized');
  }

  // 从 DurableObject 存储加载数据
  private async loadFromStorage() {
    try {
      const storedChannels = await this.state.storage.get('channels') as Channel[] | undefined;
      if (storedChannels && Array.isArray(storedChannels)) {
        // 确保保留默认频道
        const hasGeneral = storedChannels.some((c: Channel) => c.id === 'general');
        if (!hasGeneral && this.lobby.channels.length > 0) {
          storedChannels.unshift(this.lobby.channels[0]);
        }
        this.lobby.channels = storedChannels;
        console.log(`从存储加载了 ${this.lobby.channels.length} 个频道`);
      }
    } catch (error) {
      console.error('加载存储数据时出错:', error);
    }
  }

  // 保存频道数据到存储
  private async saveChannelsToStorage() {
    try {
      // 只保存频道列表，不保存用户信息
      const channelsToSave = this.lobby.channels.map(channel => ({
        ...channel,
        users: [] // 不保存用户列表，用户是临时的
      }));

      // 使用 DurableObject 存储 API
      await this.state.storage.put('channels', channelsToSave);
      console.log('频道数据已保存到存储');
    } catch (error) {
      console.error('保存频道数据到存储时出错:', error);
    }
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    
    // 处理WebSocket升级
    if (request.headers.get('Upgrade') === 'websocket') {
      return this.handleWebSocket(request);
    }
    
    // 处理REST API
    if (url.pathname.startsWith('/api/')) {
      return this.handleAPI(request);
    }
    
    return new Response('Not found', { status: 404 });
  }

  private async handleWebSocket(request: Request): Promise<Response> {
    const webSocketPair = new WebSocketPair();
    const [client, server] = Object.values(webSocketPair);
    
    const connectionId = generateId();
    this.connections.set(connectionId, server);
    
    server.accept();
    
    server.addEventListener('message', async (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data as string);
        await this.handleWebSocketMessage(connectionId, message);
      } catch (error) {
        console.error('Error handling WebSocket message:', error);
        this.sendError(server, 'Invalid message format');
      }
    });
    
    server.addEventListener('close', () => {
      this.handleDisconnection(connectionId);
    });
    
    server.addEventListener('error', (error) => {
      console.error('WebSocket error:', error);
      this.handleDisconnection(connectionId);
    });
    
    console.log(`WebSocket connection established: ${connectionId}`);
    
    return new Response(null, {
      status: 101,
      webSocket: client,
    });
  }

  private async handleAPI(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const path = url.pathname;
    
    // CORS headers
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };
    
    if (request.method === 'OPTIONS') {
      return new Response(null, { headers: corsHeaders });
    }
    
    try {
      if (path === '/api/lobby/state') {
        return new Response(JSON.stringify(this.lobby), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }
      
      return new Response('Not found', { status: 404, headers: corsHeaders });
    } catch (error) {
      console.error('API error:', error);
      return new Response('Internal server error', { status: 500, headers: corsHeaders });
    }
  }

  private async handleWebSocketMessage(connectionId: string, message: WebSocketMessage): Promise<void> {
    console.log(`Received message from ${connectionId}:`, message.type);
    
    switch (message.type) {
      case 'JOIN_LOBBY':
        await this.handleJoinLobby(connectionId, message.payload);
        break;
      case 'LEAVE_LOBBY':
        await this.handleLeaveLobby(connectionId, message.payload);
        break;
      case 'JOIN_CHANNEL':
        await this.handleJoinChannel(connectionId, message.payload);
        break;
      case 'LEAVE_CHANNEL':
        await this.handleLeaveChannel(connectionId, message.payload);
        break;
      case 'CREATE_CHANNEL':
        await this.handleCreateChannel(connectionId, message.payload);
        break;
      case 'DELETE_CHANNEL':
        await this.handleDeleteChannel(connectionId, message.payload);
        break;
      case 'RENAME_CHANNEL':
        await this.handleRenameChannel(connectionId, message.payload);
        break;
      case 'UPDATE_USER':
        await this.handleUpdateUser(connectionId, message.payload);
        break;
      case 'SIGNALING':
        await this.handleSignaling(connectionId, message.payload);
        break;
      default:
        console.log('Unknown message type:', message.type);
    }
    
    this.updateLastActivity();
  }

  private async handleJoinLobby(connectionId: string, payload: any): Promise<void> {
    const { user } = payload;
    if (!user) return;

    console.log(`User ${user.nickname} joining lobby`);

    // 检查用户是否已经在大厅中
    const existingUserIndex = this.lobby.users.findIndex(u => u.id === user.id);
    if (existingUserIndex >= 0) {
      // 更新现有用户
      this.lobby.users[existingUserIndex] = { ...user, isConnected: true };
    } else {
      // 添加新用户
      this.lobby.users.push({ ...user, isConnected: true });
    }

    this.lobby.updatedAt = formatTimestamp();

    // 发送当前大厅状态给新用户
    const ws = this.connections.get(connectionId);
    if (ws) {
      ws.send(JSON.stringify({
        type: 'LOBBY_STATE',
        payload: this.lobby,
        timestamp: formatTimestamp()
      }));
    }

    // 广播用户加入事件给其他用户
    this.broadcastEvent({
      type: 'USER_JOIN',
      payload: user
    }, connectionId);

    console.log(`User ${user.nickname} joined lobby. Total users: ${this.lobby.users.length}`);
  }

  private async handleLeaveLobby(connectionId: string, payload: any): Promise<void> {
    const { userId } = payload;
    if (!userId) return;

    // 从所有频道中移除用户
    this.lobby.channels.forEach(channel => {
      channel.users = channel.users.filter(u => u.id !== userId);
    });

    // 从大厅中移除用户
    const userIndex = this.lobby.users.findIndex(u => u.id === userId);
    if (userIndex >= 0) {
      const user = this.lobby.users[userIndex];
      this.lobby.users.splice(userIndex, 1);
      
      // 广播用户离开事件
      this.broadcastEvent({
        type: 'USER_LEAVE',
        payload: { userId }
      });
      
      console.log(`User ${user.nickname} left lobby. Total users: ${this.lobby.users.length}`);
    }

    this.lobby.updatedAt = formatTimestamp();
  }

  private async handleJoinChannel(connectionId: string, payload: any): Promise<void> {
    const { userId, channelId } = payload;
    if (!userId || !channelId) return;

    const user = this.lobby.users.find(u => u.id === userId);
    const channel = this.lobby.channels.find(c => c.id === channelId);
    
    if (!user || !channel) return;

    // 从其他频道中移除用户
    this.lobby.channels.forEach(ch => {
      if (ch.id !== channelId) {
        ch.users = ch.users.filter(u => u.id !== userId);
      }
    });

    // 添加用户到新频道
    if (!channel.users.find(u => u.id === userId)) {
      channel.users.push(user);
    }

    channel.updatedAt = formatTimestamp();
    this.lobby.updatedAt = formatTimestamp();

    // 广播频道状态更新
    this.broadcastEvent({
      type: 'CHANNEL_UPDATE',
      payload: channel
    });

    console.log(`User ${user.nickname} joined channel ${channel.name}`);
  }

  private async handleLeaveChannel(connectionId: string, payload: any): Promise<void> {
    const { userId, channelId } = payload;
    if (!userId || !channelId) return;

    const channel = this.lobby.channels.find(c => c.id === channelId);
    if (!channel) return;

    // 从频道中移除用户
    const userIndex = channel.users.findIndex(u => u.id === userId);
    if (userIndex >= 0) {
      const user = channel.users[userIndex];
      channel.users.splice(userIndex, 1);
      
      channel.updatedAt = formatTimestamp();
      this.lobby.updatedAt = formatTimestamp();

      // 广播频道状态更新
      this.broadcastEvent({
        type: 'CHANNEL_UPDATE',
        payload: channel
      });

      console.log(`User ${user.nickname} left channel ${channel.name}`);
    }
  }

  private async handleCreateChannel(connectionId: string, payload: any): Promise<void> {
    const { name, type = 'voice', createdBy } = payload;
    if (!name || !createdBy) return;

    // 检查是否存在同名频道
    const existingChannel = this.lobby.channels.find(c => c.name.toLowerCase() === name.toLowerCase());
    if (existingChannel) {
      // 发送错误消息给请求创建频道的连接
      const ws = this.connections.get(connectionId);
      if (ws) {
        this.sendError(ws, `频道"${name}"已存在，请使用其他名称`);
      }
      console.log(`频道创建失败: 名称"${name}"已被使用`);
      return;
    }

    const newChannel: Channel = {
      id: generateId(),
      name,
      type,
      users: [],
      userCount: 0,
      createdAt: formatTimestamp(),
      updatedAt: formatTimestamp(),
    };

    this.lobby.channels.push(newChannel);
    this.lobby.updatedAt = formatTimestamp();

    try {
      // 保存频道数据到存储
      await this.saveChannelsToStorage();
    } catch (error) {
      console.error('保存频道数据失败:', error);
    }

    // 广播新频道创建事件
    this.broadcastEvent({
      type: 'CHANNEL_CREATE',
      payload: newChannel
    });

    console.log(`频道"${name}"由"${createdBy}"创建成功`);
  }

  private async handleDeleteChannel(connectionId: string, payload: any): Promise<void> {
    const { channelId, deletedBy } = payload;
    if (!channelId || !deletedBy) return;

    // 不能删除默认频道
    if (channelId === 'general') return;

    const channelIndex = this.lobby.channels.findIndex(c => c.id === channelId);
    if (channelIndex >= 0) {
      const channel = this.lobby.channels[channelIndex];
      
      // 将频道中的用户移动到默认频道
      const generalChannel = this.lobby.channels.find(c => c.id === 'general');
      if (generalChannel) {
        channel.users.forEach(user => {
          if (!generalChannel.users.find(u => u.id === user.id)) {
            generalChannel.users.push(user);
          }
        });
      }

      this.lobby.channels.splice(channelIndex, 1);
      this.lobby.updatedAt = formatTimestamp();

      try {
        // 保存频道数据到存储
        await this.saveChannelsToStorage();
      } catch (error) {
        console.error('保存频道数据失败:', error);
      }

      // 广播频道删除事件
      this.broadcastEvent({
        type: 'CHANNEL_DELETE',
        payload: { channelId }
      });

      console.log(`Channel ${channel.name} deleted by ${deletedBy}`);
    }
  }

  private async handleRenameChannel(connectionId: string, payload: any): Promise<void> {
    const { channelId, newName, renamedBy } = payload;
    if (!channelId || !newName || !renamedBy) return;

    // 检查是否存在同名频道（排除要重命名的频道自己）
    const existingChannel = this.lobby.channels.find(c => 
      c.name.toLowerCase() === newName.toLowerCase() && c.id !== channelId
    );
    
    if (existingChannel) {
      // 发送错误消息给请求重命名频道的连接
      const ws = this.connections.get(connectionId);
      if (ws) {
        this.sendError(ws, `频道"${newName}"已存在，请使用其他名称`);
      }
      console.log(`频道重命名失败: 名称"${newName}"已被使用`);
      return;
    }

    const channel = this.lobby.channels.find(c => c.id === channelId);
    if (channel) {
      const oldName = channel.name;
      channel.name = newName;
      channel.updatedAt = formatTimestamp();
      this.lobby.updatedAt = formatTimestamp();

      try {
        // 保存频道数据到存储
        await this.saveChannelsToStorage();
      } catch (error) {
        console.error('保存频道数据失败:', error);
      }

      // 广播频道重命名事件
      this.broadcastEvent({
        type: 'CHANNEL_UPDATE',
        payload: channel
      });

      console.log(`频道"${oldName}"已重命名为"${newName}"，操作者: ${renamedBy}`);
    }
  }

  private async handleUpdateUser(connectionId: string, payload: any): Promise<void> {
    const { userId, updates } = payload;
    if (!userId || !updates) return;

    const user = this.lobby.users.find(u => u.id === userId);
    if (user) {
      Object.assign(user, updates);
      this.lobby.updatedAt = formatTimestamp();

      // 广播用户状态更新
      this.broadcastEvent({
        type: 'USER_UPDATE',
        payload: user
      });
    }
  }

  private async handleSignaling(connectionId: string, payload: any): Promise<void> {
    // 不再处理WebRTC信令消息，因为已经移除语音功能
    console.log('信令消息被忽略 (语音功能已移除)');
  }

  private handleDisconnection(connectionId: string): void {
    this.connections.delete(connectionId);
    console.log(`WebSocket connection closed: ${connectionId}`);
  }

  private broadcastEvent(event: any, excludeConnectionId?: string): void {
    broadcastToConnections(this.connections, {
      type: 'LOBBY_EVENT',
      payload: event,
      timestamp: formatTimestamp()
    }, excludeConnectionId);
  }

  private sendError(ws: WebSocket, error: string): void {
    ws.send(JSON.stringify({
      type: 'ERROR',
      payload: { error },
      timestamp: formatTimestamp()
    }));
  }

  private updateLastActivity(): void {
    this.lastActivity = Date.now();
  }
}
