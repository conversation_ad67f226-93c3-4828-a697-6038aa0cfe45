import { useState, useCallback, useRef } from 'react';
import { 
  CallsSession, 
  SessionDescription, 
  VoiceEvent,
  CallsTrack 
} from '../types';
import { useWebRTC } from './useWebRTC';

interface UseCloudflareVoiceProps {
  onVoiceEvent?: (event: VoiceEvent) => void;
  onError?: (error: string) => void;
  sendMessage?: (message: any) => void;
  onEvent?: (event: any) => void;
}

export function useCloudflareVoice({
  onVoiceEvent,
  onError,
  sendMessage,
  onEvent
}: UseCloudflareVoiceProps) {
  const [currentSession, setCurrentSession] = useState<CallsSession | null>(null);
  const [availableTracks, setAvailableTracks] = useState<CallsTrack[]>([]);
  const [isJoiningVoice, setIsJoiningVoice] = useState(false);
  
  const sessionIdRef = useRef<string | null>(null);
  const trackNameRef = useRef<string | null>(null);

  const webRTC = useWebRTC({
    onVoiceEvent: (event) => {
      handleVoiceEvent(event);
      onVoiceEvent?.(event);
    },
    onError,
  });

  // 加入语音频道
  const joinVoiceChannel = useCallback(async (userId: string, channelId: string) => {
    if (isJoiningVoice) return;
    
    try {
      setIsJoiningVoice(true);
      
      // 获取用户媒体流
      await webRTC.getUserMedia(webRTC.selectedInputDevice);
      
      // 发送加入语音请求到后端
      sendMessage?.({
        type: 'JOIN_VOICE',
        payload: { userId, channelId },
        timestamp: new Date().toISOString(),
        userId,
      });
      
    } catch (error) {
      console.error('Failed to join voice channel:', error);
      onError?.(`Failed to join voice channel: ${error.message}`);
      setIsJoiningVoice(false);
    }
  }, [isJoiningVoice, webRTC, sendMessage, onError]);

  // 处理语音会话创建
  const handleVoiceSessionCreated = useCallback(async (sessionData: {
    sessionId: string;
    sessionDescription: SessionDescription;
    iceServers: RTCIceServer[];
  }) => {
    try {
      // 创建 PeerConnection
      const pc = webRTC.createPeerConnection(sessionData.iceServers);
      
      // 添加本地音频轨道
      await webRTC.addLocalAudioTrack();
      
      // 设置远程描述
      await webRTC.setRemoteDescription(sessionData.sessionDescription);
      
      // 创建答案
      const answer = await webRTC.createAnswer(sessionData.sessionDescription);
      
      // 生成轨道名称
      const trackName = `audio-${Date.now()}`;
      trackNameRef.current = trackName;
      sessionIdRef.current = sessionData.sessionId;
      
      // 推送轨道到 Cloudflare
      sendMessage?.({
        type: 'PUSH_TRACK',
        payload: {
          userId: sessionIdRef.current, // 这里应该是实际的 userId
          trackName,
          sessionDescription: answer,
        },
        timestamp: new Date().toISOString(),
      });
      
      // 更新会话状态
      setCurrentSession({
        sessionId: sessionData.sessionId,
        appId: '', // 从后端获取
        tracks: [],
        created: new Date().toISOString(),
        status: 'connected',
      });
      
      setIsJoiningVoice(false);
      
    } catch (error) {
      console.error('Failed to handle voice session:', error);
      onError?.(`Failed to setup voice session: ${error.message}`);
      setIsJoiningVoice(false);
    }
  }, [webRTC, sendMessage, onError]);

  // 处理轨道推送完成
  const handleTrackPushed = useCallback(async (response: {
    sessionDescription: SessionDescription;
    trackName: string;
    mid: string;
  }) => {
    try {
      // 设置最终的远程描述
      await webRTC.setRemoteDescription(response.sessionDescription);
      
      console.log(`Track ${response.trackName} pushed successfully with MID ${response.mid}`);
      
      // 现在可以拉取其他用户的轨道
      await pullAvailableTracks();
      
    } catch (error) {
      console.error('Failed to handle track pushed:', error);
      onError?.(`Failed to complete track setup: ${error.message}`);
    }
  }, [webRTC, onError]);

  // 拉取可用的轨道
  const pullAvailableTracks = useCallback(async () => {
    if (!sessionIdRef.current) return;
    
    try {
      // 为每个可用轨道创建拉取请求
      for (const track of availableTracks) {
        if (track.location === 'local' && track.trackName !== trackNameRef.current) {
          const offer = await webRTC.createOffer();
          
          sendMessage?.({
            type: 'PULL_TRACK',
            payload: {
              userId: sessionIdRef.current,
              trackName: track.trackName,
              sessionDescription: offer,
            },
            timestamp: new Date().toISOString(),
          });
        }
      }
    } catch (error) {
      console.error('Failed to pull tracks:', error);
      onError?.(`Failed to pull audio tracks: ${error.message}`);
    }
  }, [availableTracks, webRTC, sendMessage, onError]);

  // 处理轨道拉取完成
  const handleTrackPulled = useCallback(async (response: {
    sessionDescription: SessionDescription;
    trackName: string;
    mid: string;
  }) => {
    try {
      // 设置远程描述以接收轨道
      await webRTC.setRemoteDescription(response.sessionDescription);
      
      console.log(`Track ${response.trackName} pulled successfully with MID ${response.mid}`);
      
    } catch (error) {
      console.error('Failed to handle track pulled:', error);
      onError?.(`Failed to receive audio track: ${error.message}`);
    }
  }, [webRTC, onError]);

  // 处理新轨道可用
  const handleTrackAvailable = useCallback((trackInfo: {
    userId: string;
    trackName: string;
    mid: string;
  }) => {
    const newTrack: CallsTrack = {
      trackName: trackInfo.trackName,
      mid: trackInfo.mid,
      location: 'remote',
      status: 'active',
      kind: 'audio',
    };
    
    setAvailableTracks(prev => {
      const filtered = prev.filter(t => t.trackName !== trackInfo.trackName);
      return [...filtered, newTrack];
    });
    
    // 自动拉取新的轨道
    if (sessionIdRef.current) {
      setTimeout(() => pullAvailableTracks(), 100);
    }
  }, [pullAvailableTracks]);

  // 离开语音频道
  const leaveVoiceChannel = useCallback(async (userId: string) => {
    try {
      // 发送离开语音请求
      sendMessage?.({
        type: 'LEAVE_VOICE',
        payload: { userId },
        timestamp: new Date().toISOString(),
        userId,
      });
      
      // 清理本地资源
      webRTC.cleanup();
      
      // 重置状态
      setCurrentSession(null);
      setAvailableTracks([]);
      sessionIdRef.current = null;
      trackNameRef.current = null;
      setIsJoiningVoice(false);
      
    } catch (error) {
      console.error('Failed to leave voice channel:', error);
      onError?.(`Failed to leave voice channel: ${error.message}`);
    }
  }, [sendMessage, webRTC, onError]);

  // 更新语音状态
  const updateVoiceState = useCallback((userId: string, updates: {
    isMuted?: boolean;
    isDeafened?: boolean;
    isSpeaking?: boolean;
  }) => {
    sendMessage?.({
      type: 'UPDATE_VOICE_STATE',
      payload: { userId, ...updates },
      timestamp: new Date().toISOString(),
      userId,
    });
  }, [sendMessage]);

  // 处理语音事件
  const handleVoiceEvent = useCallback((event: VoiceEvent) => {
    switch (event.type) {
      case 'VOICE_SESSION_CREATED':
        handleVoiceSessionCreated(event.payload);
        break;
      case 'TRACK_PUSHED':
        handleTrackPushed(event.payload);
        break;
      case 'TRACK_PULLED':
        handleTrackPulled(event.payload);
        break;
      case 'TRACK_AVAILABLE':
        handleTrackAvailable(event.payload);
        break;
      default:
        onVoiceEvent?.(event);
    }
  }, [
    handleVoiceSessionCreated,
    handleTrackPushed,
    handleTrackPulled,
    handleTrackAvailable,
    onVoiceEvent,
  ]);

  return {
    // 状态
    currentSession,
    availableTracks,
    isJoiningVoice,
    voiceState: webRTC.voiceState,
    audioDevices: webRTC.audioDevices,
    selectedInputDevice: webRTC.selectedInputDevice,
    selectedOutputDevice: webRTC.selectedOutputDevice,
    localStream: webRTC.localStream,

    // 方法
    joinVoiceChannel,
    leaveVoiceChannel,
    updateVoiceState,
    handleVoiceEvent,

    // WebRTC 控制
    toggleMute: webRTC.toggleMute,
    toggleDeafen: webRTC.toggleDeafen,
    setVolume: webRTC.setVolume,
    switchInputDevice: webRTC.switchInputDevice,
  };
}
