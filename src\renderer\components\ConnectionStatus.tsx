import React from 'react';

interface ConnectionStatusProps {
  isConnected: boolean;
  className?: string;
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  isConnected,
  className = '',
}) => {
  if (isConnected) {
    return null; // 连接正常时不显示
  }

  return (
    <div className={`bg-yellow-600 text-white px-4 py-2 text-sm flex items-center justify-center space-x-2 ${className}`}>
      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
      </svg>
      <span>连接中...</span>
    </div>
  );
};

export default ConnectionStatus;
