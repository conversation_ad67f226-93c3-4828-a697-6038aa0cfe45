// 生成UUID（使用Web Crypto API）
export function generateId(): string {
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }
  // 降级方案
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// 生成随机昵称
export function generateRandomNickname(): string {
  const adjectives = [
    '快乐的', '聪明的', '勇敢的', '友善的', '神秘的', '活泼的', '冷静的', '幽默的',
    '优雅的', '坚强的', '温柔的', '机智的', '热情的', '安静的', '开朗的', '谦逊的'
  ];
  
  const nouns = [
    '狐狸', '熊猫', '老虎', '狮子', '大象', '海豚', '企鹅', '猫咪',
    '小狗', '兔子', '松鼠', '猴子', '鹦鹉', '蝴蝶', '独角兽', '龙'
  ];
  
  const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
  const noun = nouns[Math.floor(Math.random() * nouns.length)];
  const number = Math.floor(Math.random() * 9999) + 1;
  
  return `${adjective}${noun}${number}`;
}



// 格式化时间
export function formatTime(date: Date): string {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  });
}

// 格式化日期
export function formatDate(date: Date): string {
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// 错误处理
export function handleError(error: any): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return '发生未知错误';
}

// 本地存储工具
export const storage = {
  get: <T>(key: string, defaultValue?: T): T | null => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue || null;
    } catch {
      return defaultValue || null;
    }
  },

  set: <T>(key: string, value: T): void => {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('存储失败:', error);
    }
  },

  remove: (key: string): void => {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('删除存储失败:', error);
    }
  },

  clear: (): void => {
    try {
      localStorage.clear();
    } catch (error) {
      console.error('清空存储失败:', error);
    }
  }
};

// 用户数据持久化
export const userStorage = {
  // 存储完整的用户对象
  saveUser: (user: any): void => {
    storage.set('opz_user', user);
  },

  // 获取完整的用户对象
  getUser: (): any | null => {
    return storage.get('opz_user');
  },

  // 存储用户偏好设置
  saveUserPreferences: (preferences: {
    nickname?: string;
  }): void => {
    storage.set('user_preferences', preferences);
  },

  // 获取用户偏好设置
  getUserPreferences: () => {
    return storage.get('user_preferences', {});
  },

  // 存储最后使用的昵称
  saveLastNickname: (nickname: string): void => {
    storage.set('last_nickname', nickname);
  },

  // 获取最后使用的昵称
  getLastNickname: (): string | null => {
    return storage.get('last_nickname');
  },

  // 存储用户统计信息
  saveUserStats: (stats: {
    totalSessions?: number;
    totalTimeSpent?: number;
    lastActiveDate?: string;
    favoriteChannels?: string[];
  }): void => {
    const currentStats = storage.get('user_stats', {
      totalSessions: 0,
      totalTimeSpent: 0,
      lastActiveDate: new Date().toISOString(),
      favoriteChannels: [],
    });

    storage.set('user_stats', { ...currentStats, ...stats });
  },

  // 获取用户统计信息
  getUserStats: () => {
    return storage.get('user_stats', {
      totalSessions: 0,
      totalTimeSpent: 0,
      lastActiveDate: new Date().toISOString(),
      favoriteChannels: [],
    });
  },

  // 增加会话计数
  incrementSessionCount: (): void => {
    const stats = userStorage.getUserStats();
    if (stats) {
      userStorage.saveUserStats({
        totalSessions: stats.totalSessions + 1,
        lastActiveDate: new Date().toISOString(),
      });
    }
  }
};
