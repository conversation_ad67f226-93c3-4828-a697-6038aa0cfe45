import React, { useEffect, useRef, useState } from 'react';

interface VoiceActivityIndicatorProps {
  audioStream?: MediaStream;
  isSpeaking: boolean;
  isConnected: boolean;
  isMuted: boolean;
  size?: 'small' | 'medium' | 'large';
  showWaveform?: boolean;
  className?: string;
}

const VoiceActivityIndicator: React.FC<VoiceActivityIndicatorProps> = ({
  audioStream,
  isSpeaking,
  isConnected,
  isMuted,
  size = 'medium',
  showWaveform = false,
  className = '',
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const analyserRef = useRef<AnalyserNode>();
  const audioContextRef = useRef<AudioContext>();
  const [audioLevel, setAudioLevel] = useState(0);

  const sizeConfig = {
    small: { width: 20, height: 20, bars: 3 },
    medium: { width: 32, height: 32, bars: 5 },
    large: { width: 48, height: 48, bars: 7 },
  };

  const config = sizeConfig[size];

  // 初始化音频分析
  useEffect(() => {
    if (!audioStream || !showWaveform) return;

    const setupAudioAnalysis = async () => {
      try {
        const audioContext = new AudioContext();
        const analyser = audioContext.createAnalyser();
        const source = audioContext.createMediaStreamSource(audioStream);
        
        analyser.fftSize = 256;
        analyser.smoothingTimeConstant = 0.8;
        source.connect(analyser);
        
        audioContextRef.current = audioContext;
        analyserRef.current = analyser;
        
        startAnalysis();
      } catch (error) {
        console.error('Failed to setup audio analysis:', error);
      }
    };

    setupAudioAnalysis();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, [audioStream, showWaveform]);

  const startAnalysis = () => {
    if (!analyserRef.current) return;

    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
    
    const analyze = () => {
      if (!analyserRef.current) return;
      
      analyserRef.current.getByteFrequencyData(dataArray);
      
      // 计算平均音频级别
      const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
      setAudioLevel(average / 255); // 归一化到 0-1
      
      if (showWaveform) {
        drawWaveform(dataArray);
      }
      
      animationRef.current = requestAnimationFrame(analyze);
    };
    
    analyze();
  };

  const drawWaveform = (dataArray: Uint8Array) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = config.width;
    canvas.height = config.height;

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    if (!isConnected) {
      drawStaticBars(ctx, '#6B7280');
      return;
    }

    if (isMuted) {
      drawStaticBars(ctx, '#EF4444');
      return;
    }

    // 绘制实时波形
    const barWidth = 2;
    const barSpacing = 1;
    const totalBarsWidth = config.bars * barWidth + (config.bars - 1) * barSpacing;
    const startX = (config.width - totalBarsWidth) / 2;

    ctx.fillStyle = isSpeaking ? '#10B981' : '#3B82F6';

    for (let i = 0; i < config.bars; i++) {
      const dataIndex = Math.floor((i / config.bars) * dataArray.length);
      const value = dataArray[dataIndex] / 255;
      
      const height = Math.max(config.height * 0.1, value * config.height * 0.8);
      const x = startX + i * (barWidth + barSpacing);
      const y = (config.height - height) / 2;

      ctx.fillRect(x, y, barWidth, height);
    }
  };

  const drawStaticBars = (ctx: CanvasRenderingContext2D, color: string) => {
    const barWidth = 2;
    const barSpacing = 1;
    const totalBarsWidth = config.bars * barWidth + (config.bars - 1) * barSpacing;
    const startX = (config.width - totalBarsWidth) / 2;
    const baseHeight = config.height * 0.3;

    ctx.fillStyle = color;

    for (let i = 0; i < config.bars; i++) {
      const x = startX + i * (barWidth + barSpacing);
      const height = baseHeight;
      const y = (config.height - height) / 2;

      ctx.fillRect(x, y, barWidth, height);
    }
  };

  // 简单的圆形指示器（当不显示波形时）
  const renderSimpleIndicator = () => {
    let indicatorColor = 'bg-gray-400';
    let pulseClass = '';

    if (!isConnected) {
      indicatorColor = 'bg-gray-400';
    } else if (isMuted) {
      indicatorColor = 'bg-red-400';
    } else if (isSpeaking) {
      indicatorColor = 'bg-green-400';
      pulseClass = 'animate-pulse';
    } else {
      indicatorColor = 'bg-blue-400';
    }

    const sizeClass = {
      small: 'w-3 h-3',
      medium: 'w-4 h-4',
      large: 'w-6 h-6',
    }[size];

    return (
      <div className={`${sizeClass} ${indicatorColor} rounded-full ${pulseClass}`} />
    );
  };

  const getTooltipText = () => {
    if (!isConnected) return '未连接语音';
    if (isMuted) return '已静音';
    if (isSpeaking) return '说话中';
    return '已连接语音';
  };

  return (
    <div 
      className={`relative inline-flex items-center justify-center ${className}`}
      title={getTooltipText()}
    >
      {showWaveform ? (
        <canvas
          ref={canvasRef}
          style={{ 
            width: config.width, 
            height: config.height,
            filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3))'
          }}
        />
      ) : (
        renderSimpleIndicator()
      )}
      
      {/* 音频级别指示器 */}
      {showWaveform && audioLevel > 0 && (
        <div 
          className="absolute inset-0 rounded-full border-2 border-green-400"
          style={{
            opacity: audioLevel,
            transform: `scale(${1 + audioLevel * 0.2})`,
            transition: 'all 0.1s ease-out'
          }}
        />
      )}
      
      {/* 连接状态指示器 */}
      {isConnected && (
        <div className="absolute -top-1 -right-1">
          <div className={`w-2 h-2 rounded-full ${
            isSpeaking ? 'bg-green-400' : 'bg-blue-400'
          }`} />
        </div>
      )}
      
      {/* 静音指示器 */}
      {isMuted && (
        <div className="absolute -bottom-1 -right-1">
          <svg className="w-3 h-3 text-red-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.797-3.793a1 1 0 011.617.793zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </div>
      )}
    </div>
  );
};

export default VoiceActivityIndicator;
