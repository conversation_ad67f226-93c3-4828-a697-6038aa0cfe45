import React, { useEffect } from 'react';
import { User, Channel, ChannelState } from '../types';
import UserAvatar from './UserAvatar';

interface MainContentProps {
  currentChannel: Channel | null;
  user: User;
  channelState: ChannelState;
  onLeaveChannel: () => void;
}

const MainContent: React.FC<MainContentProps> = ({ 
  currentChannel, 
  user, 
  channelState, 
  onLeaveChannel 
}) => {
  // 添加调试日志
  useEffect(() => {
    if (currentChannel) {
      console.log('当前频道信息:', currentChannel);
      console.log('频道用户:', currentChannel.users);
    }
  }, [currentChannel]);

  if (!currentChannel) {
    return (
      <div className="flex-1 bg-discord-gray-700 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-discord-gray-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-discord-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM15.657 6.343a1 1 0 011.414 0A9.972 9.972 0 0119 12a9.972 9.972 0 01-1.929 5.657 1 1 0 11-1.414-1.414A7.971 7.971 0 0017 12c0-1.594-.471-3.078-1.343-4.243a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-white mb-2">选择一个频道</h2>
          <p className="text-discord-gray-400">
            从左侧选择一个频道进行交流
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 bg-discord-gray-700 flex flex-col">
      {/* 频道标题栏 */}
      <div className="h-12 px-4 flex items-center justify-between border-b border-discord-gray-600 bg-discord-gray-800">
        <div className="flex items-center">
          <svg className="w-5 h-5 mr-2 text-discord-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM15.657 6.343a1 1 0 011.414 0A9.972 9.972 0 0119 12a9.972 9.972 0 01-1.929 5.657 1 1 0 11-1.414-1.414A7.971 7.971 0 0017 12c0-1.594-.471-3.078-1.343-4.243a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
          <h2 className="text-lg font-semibold text-white">{currentChannel.name}</h2>
        </div>
        
        {/* 退出频道按钮 */}
        {channelState.isConnected && (
          <button
            onClick={onLeaveChannel}
            className="flex items-center px-3 py-1 bg-discord-gray-600 hover:bg-discord-gray-500 text-white text-sm rounded transition-colors duration-150"
            title="退出频道"
          >
            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clipRule="evenodd" />
            </svg>
            退出频道
          </button>
        )}
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex">
        {/* 侧边栏 - 频道信息 */}
        <div className="flex-1 bg-discord-gray-800 p-4">
          <div className="text-center">
            <div className="w-12 h-12 bg-discord-gray-600 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-discord-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
              </svg>
            </div>
            <h4 className="text-sm font-medium text-white mb-2">频道信息</h4>
            <p className="text-xs text-discord-gray-400 mb-4">
              {`${currentChannel.name} (${currentChannel.type === 'voice' ? '语音' : '文本'}频道)`}
            </p>
            
            <div className="space-y-3">
              <div className="text-left">
                <label className="block text-xs font-medium text-discord-gray-400 mb-1">
                  创建时间
                </label>
                <div className="text-xs text-white">
                  {new Date(currentChannel.createdAt).toLocaleString()}
                </div>
              </div>
              
              <div className="text-left">
                <label className="block text-xs font-medium text-discord-gray-400 mb-1">
                  在线用户数
                </label>
                <div className="text-xs text-white">
                  {currentChannel.userCount + 1} 人
                </div>
              </div>
              
              {/* 连接状态 */}
              <div className="text-left">
                <label className="block text-xs font-medium text-discord-gray-400 mb-1">
                  连接状态
                </label>
                <div className="text-xs text-white">
                  {channelState.isConnected ? (
                    <span className="text-green-500">已连接</span>
                  ) : (
                    <span className="text-yellow-500">未连接</span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MainContent;
