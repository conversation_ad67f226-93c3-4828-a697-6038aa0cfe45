import React, { useState } from 'react';
import { VoiceState, AudioDevice } from '../types';
import VoiceActivityIndicator from './VoiceActivityIndicator';

interface VoiceControlsProps {
  voiceState: VoiceState;
  audioDevices: AudioDevice[];
  selectedInputDevice: string;
  selectedOutputDevice: string;
  localStream?: MediaStream;
  onToggleMute: () => void;
  onToggleDeafen: () => void;
  onVolumeChange: (volume: number) => void;
  onInputDeviceChange: (deviceId: string) => void;
  onOutputDeviceChange: (deviceId: string) => void;
  onJoinVoice?: () => void;
  onLeaveVoice?: () => void;
  className?: string;
}

const VoiceControls: React.FC<VoiceControlsProps> = ({
  voiceState,
  audioDevices,
  selectedInputDevice,
  selectedOutputDevice,
  localStream,
  onToggleMute,
  onToggleDeafen,
  onVolumeChange,
  onInputDeviceChange,
  onOutputDeviceChange,
  onJoinVoice,
  onLeaveVoice,
  className = '',
}) => {
  const [showSettings, setShowSettings] = useState(false);
  const [showDeviceSettings, setShowDeviceSettings] = useState(false);

  const inputDevices = audioDevices.filter(device => device.kind === 'audioinput');
  const outputDevices = audioDevices.filter(device => device.kind === 'audiooutput');

  const getMuteIcon = () => {
    if (voiceState.isMuted) {
      return (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.797-3.793a1 1 0 011.617.793zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      );
    }
    return (
      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.797-3.793a1 1 0 011.617.793zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" clipRule="evenodd" />
      </svg>
    );
  };

  const getDeafenIcon = () => {
    if (voiceState.isDeafened) {
      return (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
          <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
          <path d="M3 3l14 14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
        </svg>
      );
    }
    return (
      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
        <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
      </svg>
    );
  };

  const getConnectionIcon = () => {
    if (voiceState.isConnected) {
      return (
        <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
        </svg>
      );
    }
    return (
      <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
      </svg>
    );
  };

  return (
    <div className={`bg-discord-gray-800 border-t border-discord-gray-700 ${className}`}>
      {/* 主控制栏 */}
      <div className="flex items-center justify-between px-4 py-3">
        {/* 连接状态和语音活动指示器 */}
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            {getConnectionIcon()}
            <span className="text-sm text-gray-300">
              {voiceState.isConnected ? '已连接' : '未连接'}
            </span>
          </div>

          {/* 实时语音活动指示器 */}
          {voiceState.isConnected && (
            <VoiceActivityIndicator
              audioStream={localStream}
              isSpeaking={voiceState.isSpeaking}
              isConnected={voiceState.isConnected}
              isMuted={voiceState.isMuted}
              size="medium"
              showWaveform={true}
            />
          )}

          {voiceState.isSpeaking && (
            <span className="text-xs text-green-400">说话中</span>
          )}
        </div>

        {/* 控制按钮 */}
        <div className="flex items-center space-x-2">
          {/* 静音按钮 */}
          <button
            onClick={onToggleMute}
            className={`p-2 rounded-md transition-colors ${
              voiceState.isMuted
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-discord-gray-600 hover:bg-discord-gray-500 text-gray-300'
            }`}
            title={voiceState.isMuted ? '取消静音' : '静音'}
          >
            {getMuteIcon()}
          </button>

          {/* 关闭声音按钮 */}
          <button
            onClick={onToggleDeafen}
            className={`p-2 rounded-md transition-colors ${
              voiceState.isDeafened
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-discord-gray-600 hover:bg-discord-gray-500 text-gray-300'
            }`}
            title={voiceState.isDeafened ? '开启声音' : '关闭声音'}
          >
            {getDeafenIcon()}
          </button>

          {/* 设置按钮 */}
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-2 rounded-md bg-discord-gray-600 hover:bg-discord-gray-500 text-gray-300 transition-colors"
            title="语音设置"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
            </svg>
          </button>

          {/* 加入/离开语音按钮 */}
          {voiceState.isConnected ? (
            <button
              onClick={onLeaveVoice}
              className="px-3 py-2 rounded-md bg-red-600 hover:bg-red-700 text-white text-sm transition-colors"
            >
              离开语音
            </button>
          ) : (
            <button
              onClick={onJoinVoice}
              className="px-3 py-2 rounded-md bg-green-600 hover:bg-green-700 text-white text-sm transition-colors"
            >
              加入语音
            </button>
          )}
        </div>
      </div>

      {/* 设置面板 */}
      {showSettings && (
        <div className="border-t border-discord-gray-700 p-4 space-y-4">
          {/* 音量控制 */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              音量: {Math.round(voiceState.volume * 100)}%
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.01"
              value={voiceState.volume}
              onChange={(e) => onVolumeChange(parseFloat(e.target.value))}
              className="w-full h-2 bg-discord-gray-600 rounded-lg appearance-none cursor-pointer slider"
            />
          </div>

          {/* 设备设置按钮 */}
          <button
            onClick={() => setShowDeviceSettings(!showDeviceSettings)}
            className="w-full px-3 py-2 text-left bg-discord-gray-600 hover:bg-discord-gray-500 text-gray-300 rounded-md transition-colors"
          >
            音频设备设置
          </button>

          {/* 设备设置面板 */}
          {showDeviceSettings && (
            <div className="space-y-3 pl-4 border-l-2 border-discord-gray-600">
              {/* 输入设备 */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  麦克风
                </label>
                <select
                  value={selectedInputDevice}
                  onChange={(e) => onInputDeviceChange(e.target.value)}
                  className="w-full px-3 py-2 bg-discord-gray-700 text-gray-300 rounded-md border border-discord-gray-600 focus:border-blue-500 focus:outline-none"
                >
                  {inputDevices.map((device) => (
                    <option key={device.deviceId} value={device.deviceId}>
                      {device.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* 输出设备 */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  扬声器
                </label>
                <select
                  value={selectedOutputDevice}
                  onChange={(e) => onOutputDeviceChange(e.target.value)}
                  className="w-full px-3 py-2 bg-discord-gray-700 text-gray-300 rounded-md border border-discord-gray-600 focus:border-blue-500 focus:outline-none"
                >
                  {outputDevices.map((device) => (
                    <option key={device.deviceId} value={device.deviceId}>
                      {device.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default VoiceControls;
