// 用户类型
export interface User {
  id: string;
  nickname: string;
  isConnected: boolean;
  joinedAt: Date;
}

// 频道类型
export interface Channel {
  id: string;
  name: string;
  type: 'voice' | 'text';
  userCount: number;
  users: User[];
  createdAt: Date;
  updatedAt: Date;
}

// 房间类型
export interface Room {
  id: string;
  name: string;
  channels: Channel[];
  users: User[];
  createdAt: Date;
  updatedAt: Date;
}

// 频道状态类型 (重命名自VoiceState，并简化)
export interface ChannelState {
  isConnected: boolean;
  currentChannelId?: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Cloudflare Calls API相关类型
export interface CallsSession {
  sessionId: string;
  appId: string;
  tracks: CallsTrack[];
  created: string;
  status: 'new' | 'connected' | 'disconnected';
}

export interface CallsTrack {
  trackName: string;
  mid: string;
  location: 'local' | 'remote';
  sessionId?: string;
  status: 'active' | 'inactive' | 'waiting';
  kind: 'audio' | 'video';
}

export interface SessionDescription {
  sdp: string;
  type: 'offer' | 'answer';
}

// 语音状态相关类型
export interface VoiceState {
  isConnected: boolean;
  sessionId?: string;
  isMuted: boolean;
  isDeafened: boolean;
  isSpeaking: boolean;
  volume: number;
  currentChannelId?: string;
}

export interface VoiceUser {
  userId: string;
  sessionId?: string;
  isMuted: boolean;
  isDeafened: boolean;
  isSpeaking: boolean;
  tracks: CallsTrack[];
}

// WebRTC 相关事件类型
export interface VoiceEvent {
  type: 'VOICE_SESSION_CREATED' | 'TRACK_PUSHED' | 'TRACK_PULLED' | 'TRACK_AVAILABLE' |
        'USER_JOINED_VOICE' | 'USER_LEFT_VOICE' | 'USER_VOICE_STATE_UPDATE';
  payload: any;
  timestamp: string;
}

// 音频设备类型
export interface AudioDevice {
  deviceId: string;
  label: string;
  kind: 'audioinput' | 'audiooutput';
}

// 应用状态类型
export interface AppState {
  user: User | null;
  room: Room | null;
  currentChannel: Channel | null;
  channelState: ChannelState;
  isLoading: boolean;
  error: string | null;
}

// 事件类型
export type AppEvent =
  | { type: 'USER_JOIN'; payload: User }
  | { type: 'USER_LEAVE'; payload: { userId: string } }
  | { type: 'USER_UPDATE'; payload: User }
  | { type: 'USER_LIST_UPDATE'; payload: User[] }
  | { type: 'USER_JOIN_CHANNEL'; payload: { userId: string, channelId: string, user: User } }
  | { type: 'USER_LEAVE_CHANNEL'; payload: { userId: string, channelId: string } }
  | { type: 'CHANNEL_CREATE'; payload: Channel }
  | { type: 'CHANNEL_DELETE'; payload: { channelId: string } }
  | { type: 'CHANNEL_UPDATE'; payload: Channel }
  | { type: 'CHANNEL_LIST_UPDATE'; payload: Channel[] }
  | { type: 'ROOM_STATE_UPDATE'; payload: Room }
  | { type: 'CHANNEL_STATE_UPDATE'; payload: Partial<ChannelState> }
  | { type: 'ERROR'; payload: { error: string } };
